# auth_app.py
import sys
from PyQt6.QtWidgets import (
    QApplication, QWidget, QLabel, QLineEdit,
    QPushButton, QVBoxLayout, QMessageBox, QCheckBox
)
from PyQt6.QtCore import Qt


# Fenêtre d'authentification
class AuthWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Authentification")
        self.resize(300, 200)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Titre
        title = QLabel("Connexion")
        title.setStyleSheet("font-size: 18px; font-weight: bold;")
        layout.addWidget(title)

        # Champs utilisateur/mot de passe
        self.username = QLineEdit()
        self.username.setPlaceholderText("Nom d'utilisateur")
        self.password = QLineEdit()
        self.password.setPlaceholderText("Mot de passe")
        self.password.setEchoMode(QLineEdit.EchoMode.Password)

        # Checkbox pour afficher le mot de passe
        self.show_password = QCheckBox("Afficher le mot de passe")
        self.show_password.toggled.connect(self.toggle_password_visibility)

        # Bouton de connexion
        login_btn = QPushButton("Se connecter")
        login_btn.setStyleSheet("background-color: #4CAF50; color: white; padding: 10px;")
        login_btn.clicked.connect(self.check_login)

        # Ajout au layout
        layout.addWidget(QLabel("Utilisateur :"))
        layout.addWidget(self.username)
        layout.addWidget(QLabel("Mot de passe :"))
        layout.addWidget(self.password)
        layout.addWidget(self.show_password)
        layout.addWidget(login_btn)

        self.setLayout(layout)
        self.setStyleSheet("""
            QWidget {
                background-color: #f9f9f9;
                font-family: Arial;
                font-size: 14px;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

    def toggle_password_visibility(self, checked):
        if checked:
            self.password.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.password.setEchoMode(QLineEdit.EchoMode.Password)

    def check_login(self):
        username = self.username.text()
        password = self.password.text()

        # Exemple d'authentification statique
        if username == "admin" and password == "1234":
            self.open_main_window()
        else:
            QMessageBox.warning(self, "Erreur", "Nom d'utilisateur ou mot de passe incorrect")

    def open_main_window(self):
        self.main_window = MainWindow()
        self.main_window.show()
        self.close()


# Fenêtre principale après authentification
class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Application principale")
        self.resize(600, 400)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        welcome = QLabel("Bienvenue dans l'application !")
        welcome.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome.setStyleSheet("font-size: 18px; font-weight: bold;")
        layout.addWidget(welcome)
        self.setLayout(layout)
        self.setStyleSheet("""
            QWidget {
                background-color: #f9f9f9;
                font-family: Arial;
                font-size: 14px;
            }
        """)


# Point d'entrée de l'application
if __name__ == "__main__":
    app = QApplication(sys.argv)
    auth_window = AuthWindow()
    auth_window.show()
    sys.exit(app.exec())